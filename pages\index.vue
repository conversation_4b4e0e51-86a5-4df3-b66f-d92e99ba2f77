<template>
  <div class="software-dev-page">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <h2>DevStudio</h2>
        </div>
        <ul class="nav-menu">
          <li><a href="#home" @click="scrollToSection('home')">首页</a></li>
          <li><a href="#services" @click="scrollToSection('services')">服务</a></li>
          <li><a href="#tech" @click="scrollToSection('tech')">技术</a></li>
          <li><a href="#projects" @click="scrollToSection('projects')">项目</a></li>
          <li><a href="#team" @click="scrollToSection('team')">团队</a></li>
          <li><a href="#contact" @click="scrollToSection('contact')">联系</a></li>
        </ul>
      </div>
    </nav>

    <!-- 英雄区域 -->
    <section id="home" class="hero">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">专业软件开发服务</h1>
          <p class="hero-subtitle">我们致力于为您提供高质量的软件解决方案，从概念到部署的全流程服务</p>
          <div class="hero-buttons">
            <button class="btn btn-primary" @click="scrollToSection('contact')">开始合作</button>
            <button class="btn btn-secondary" @click="scrollToSection('projects')">查看案例</button>
          </div>
        </div>
        <div class="hero-image">
          <div class="code-animation">
            <div class="code-line" v-for="(line, index) in codeLines" :key="index" :style="{ animationDelay: index * 0.5 + 's' }">
              {{ line }}
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 服务介绍 -->
    <section id="services" class="services">
      <div class="container">
        <h2 class="section-title">我们的服务</h2>
        <div class="services-grid">
          <div class="service-card" v-for="service in services" :key="service.id">
            <div class="service-icon">{{ service.icon }}</div>
            <h3>{{ service.title }}</h3>
            <p>{{ service.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术栈 -->
    <section id="tech" class="tech-stack">
      <div class="container">
        <h2 class="section-title">技术栈</h2>
        <div class="tech-categories">
          <div class="tech-category" v-for="category in techStack" :key="category.name">
            <h3>{{ category.name }}</h3>
            <div class="tech-items">
              <div class="tech-item" v-for="tech in category.technologies" :key="tech.name">
                <div class="tech-logo">{{ tech.icon }}</div>
                <span>{{ tech.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 项目案例 -->
    <section id="projects" class="projects">
      <div class="container">
        <h2 class="section-title">项目案例</h2>
        <div class="projects-grid">
          <div class="project-card" v-for="project in projects" :key="project.id">
            <div class="project-image">
              <div class="project-placeholder">{{ project.type }}</div>
            </div>
            <div class="project-content">
              <h3>{{ project.title }}</h3>
              <p>{{ project.description }}</p>
              <div class="project-tech">
                <span v-for="tech in project.technologies" :key="tech" class="tech-tag">{{ tech }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 团队介绍 -->
    <section id="team" class="team">
      <div class="container">
        <h2 class="section-title">我们的团队</h2>
        <div class="team-grid">
          <div class="team-member" v-for="member in team" :key="member.id">
            <div class="member-avatar">{{ member.avatar }}</div>
            <h3>{{ member.name }}</h3>
            <p class="member-role">{{ member.role }}</p>
            <p class="member-description">{{ member.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系方式 -->
    <section id="contact" class="contact">
      <div class="container">
        <h2 class="section-title">联系我们</h2>
        <div class="contact-content">
          <div class="contact-info">
            <h3>让我们开始合作</h3>
            <p>有项目想法？让我们一起讨论如何将其变为现实。</p>
            <div class="contact-details">
              <div class="contact-item">
                <span class="contact-icon">📧</span>
                <span><EMAIL></span>
              </div>
              <div class="contact-item">
                <span class="contact-icon">📱</span>
                <span>+86 138 0000 0000</span>
              </div>
              <div class="contact-item">
                <span class="contact-icon">📍</span>
                <span>北京市朝阳区科技园</span>
              </div>
            </div>
          </div>
          <div class="contact-form">
            <form @submit.prevent="submitForm">
              <div class="form-group">
                <input type="text" v-model="form.name" placeholder="您的姓名" required>
              </div>
              <div class="form-group">
                <input type="email" v-model="form.email" placeholder="邮箱地址" required>
              </div>
              <div class="form-group">
                <textarea v-model="form.message" placeholder="项目描述" rows="5" required></textarea>
              </div>
              <button type="submit" class="btn btn-primary">发送消息</button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>DevStudio</h3>
            <p>专业的软件开发团队，为您提供优质的技术解决方案。</p>
          </div>
          <div class="footer-section">
            <h4>服务</h4>
            <ul>
              <li>Web开发</li>
              <li>移动应用</li>
              <li>系统集成</li>
              <li>技术咨询</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>技术</h4>
            <ul>
              <li>Vue.js / Nuxt.js</li>
              <li>React / Next.js</li>
              <li>Node.js</li>
              <li>Python</li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 DevStudio. 保留所有权利。</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
// 页面数据
const codeLines = ref([
  'const app = createApp({',
  '  data() {',
  '    return {',
  '      message: "Hello World!"',
  '    }',
  '  }',
  '})'
])

const services = ref([
  {
    id: 1,
    icon: '🌐',
    title: 'Web开发',
    description: '响应式网站和Web应用程序开发，使用最新的前端和后端技术。'
  },
  {
    id: 2,
    icon: '📱',
    title: '移动应用开发',
    description: '原生和跨平台移动应用开发，支持iOS和Android平台。'
  },
  {
    id: 3,
    icon: '☁️',
    title: '云服务集成',
    description: '云平台部署、微服务架构设计和DevOps自动化解决方案。'
  },
  {
    id: 4,
    icon: '🔧',
    title: '系统维护',
    description: '系统优化、性能调优、安全加固和技术支持服务。'
  },
  {
    id: 5,
    icon: '💡',
    title: '技术咨询',
    description: '技术架构设计、代码审查和最佳实践指导。'
  },
  {
    id: 6,
    icon: '🎨',
    title: 'UI/UX设计',
    description: '用户界面设计、用户体验优化和交互设计服务。'
  }
])

const techStack = ref([
  {
    name: '前端技术',
    technologies: [
      { name: 'Vue.js', icon: '🟢' },
      { name: 'React', icon: '⚛️' },
      { name: 'TypeScript', icon: '🔷' },
      { name: 'Nuxt.js', icon: '💚' }
    ]
  },
  {
    name: '后端技术',
    technologies: [
      { name: 'Node.js', icon: '🟩' },
      { name: 'Python', icon: '🐍' },
      { name: 'Java', icon: '☕' },
      { name: 'Go', icon: '🐹' }
    ]
  },
  {
    name: '数据库',
    technologies: [
      { name: 'MongoDB', icon: '🍃' },
      { name: 'PostgreSQL', icon: '🐘' },
      { name: 'Redis', icon: '🔴' },
      { name: 'MySQL', icon: '🐬' }
    ]
  },
  {
    name: '云服务',
    technologies: [
      { name: 'AWS', icon: '☁️' },
      { name: 'Docker', icon: '🐳' },
      { name: 'Kubernetes', icon: '⚙️' },
      { name: 'Vercel', icon: '▲' }
    ]
  }
])

const projects = ref([
  {
    id: 1,
    title: '电商平台',
    description: '全功能的在线购物平台，支持多商户、支付集成和订单管理。',
    type: '🛒',
    technologies: ['Vue.js', 'Node.js', 'MongoDB', 'Stripe']
  },
  {
    id: 2,
    title: '企业管理系统',
    description: '综合性企业资源规划系统，包含人事、财务、项目管理模块。',
    type: '📊',
    technologies: ['React', 'Python', 'PostgreSQL', 'Docker']
  },
  {
    id: 3,
    title: '移动社交应用',
    description: '跨平台社交应用，支持实时聊天、动态分享和位置服务。',
    type: '📱',
    technologies: ['React Native', 'Firebase', 'Node.js', 'Socket.io']
  }
])

const team = ref([
  {
    id: 1,
    name: '张伟',
    role: '技术总监',
    avatar: '👨‍💻',
    description: '10年+全栈开发经验，专注于系统架构设计和团队管理。'
  },
  {
    id: 2,
    name: '李娜',
    role: '前端架构师',
    avatar: '👩‍💻',
    description: '前端技术专家，精通现代前端框架和用户体验设计。'
  },
  {
    id: 3,
    name: '王强',
    role: '后端工程师',
    avatar: '👨‍🔧',
    description: '后端开发专家，擅长微服务架构和数据库优化。'
  },
  {
    id: 4,
    name: '赵敏',
    role: 'UI/UX设计师',
    avatar: '👩‍🎨',
    description: '资深设计师，专注于用户界面设计和交互体验优化。'
  }
])

const form = ref({
  name: '',
  email: '',
  message: ''
})

// 方法
const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const submitForm = () => {
  // 这里可以添加表单提交逻辑
  alert('感谢您的留言！我们会尽快与您联系。')
  form.value = { name: '', email: '', message: '' }
}

// 页面标题和SEO
useHead({
  title: 'DevStudio - 专业软件开发服务',
  meta: [
    { name: 'description', content: '专业的软件开发团队，提供Web开发、移动应用、云服务集成等全方位技术解决方案。' },
    { name: 'keywords', content: '软件开发,Web开发,移动应用,Vue.js,React,Node.js,技术咨询' }
  ]
})
</script>

<style scoped>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.software-dev-page {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 导航栏 */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo h2 {
  color: #2563eb;
  font-size: 1.5rem;
  font-weight: bold;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-menu a:hover {
  color: #2563eb;
}

/* 英雄区域 */
.hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #2563eb;
  color: white;
}

.btn-primary:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: #2563eb;
}

/* 代码动画 */
.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.code-animation {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12px;
  padding: 2rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  min-width: 300px;
}

.code-line {
  opacity: 0;
  animation: fadeInUp 1s ease forwards;
  margin-bottom: 0.5rem;
  color: #00ff88;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 通用区域样式 */
section {
  padding: 80px 0;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

/* 服务区域 */
.services {
  background: #f8fafc;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.service-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.service-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.service-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

.service-card p {
  color: #6b7280;
  line-height: 1.6;
}

/* 技术栈区域 */
.tech-stack {
  background: white;
}

.tech-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.tech-category h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #1f2937;
  text-align: center;
}

.tech-items {
  display: grid;
  gap: 1rem;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.tech-item:hover {
  background: #e2e8f0;
}

.tech-logo {
  font-size: 1.5rem;
}

.tech-item span {
  font-weight: 500;
  color: #374151;
}

/* 项目区域 */
.projects {
  background: #f8fafc;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.project-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.project-image {
  height: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-placeholder {
  font-size: 4rem;
  color: white;
}

.project-content {
  padding: 1.5rem;
}

.project-content h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

.project-content p {
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  background: #e2e8f0;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 团队区域 */
.team {
  background: white;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.team-member {
  text-align: center;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
}

.member-avatar {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.team-member h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.member-role {
  color: #2563eb;
  font-weight: 600;
  margin-bottom: 1rem;
}

.member-description {
  color: #6b7280;
  line-height: 1.6;
}

/* 联系区域 */
.contact {
  background: #1f2937;
  color: white;
}

.contact .section-title {
  color: white;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-info h3 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.contact-info p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.contact-icon {
  font-size: 1.5rem;
}

.contact-form {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  transition: background 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  background: white;
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* 页脚 */
.footer {
  background: #111827;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1rem;
  color: #f9fafb;
}

.footer-section h3 {
  font-size: 1.5rem;
  color: #2563eb;
}

.footer-section p {
  opacity: 0.8;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.footer-section ul li:hover {
  opacity: 1;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 1rem;
  text-align: center;
  opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-buttons {
    justify-content: center;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .tech-categories {
    grid-template-columns: 1fr;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .team-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .container {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }

  .service-card,
  .team-member {
    padding: 1.5rem;
  }

  .contact-form {
    padding: 1.5rem;
  }
}

/* 滚动行为 */
html {
  scroll-behavior: smooth;
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s infinite;
}
</style>