<template>
  <div class="presentation">
    <div v-if="currentSlide === 1" class="slide">
      <h1>我的演示文稿</h1>
      <p>使用 Nuxt 和 Vite 构建</p>
    </div>
    <div v-if="currentSlide === 2" class="slide">
      <h2>第二页</h2>
      <ul>
        <li>要点 1</li>
        <li>要点 2</li>
      </ul>
    </div>
    <!-- 更多幻灯片 -->
    <div class="controls">
      <button @click="prevSlide" :disabled="currentSlide <= 1">上一页</button>
      <span>{{ currentSlide }}/5</span>
      <button @click="nextSlide" :disabled="currentSlide >= 5">下一页</button>
    </div>
  </div>
</template>

<script setup>
const currentSlide = ref(1)
const totalSlides = 5

function nextSlide() {
  if (currentSlide.value < totalSlides) {
    currentSlide.value++
  }
}

function prevSlide() {
  if (currentSlide.value > 1) {
    currentSlide.value--
  }
}
</script>