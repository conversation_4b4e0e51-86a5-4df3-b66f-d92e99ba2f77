export function useKeyboardNavigation(nextSlide: () => void, prevSlide: () => void) {
  onMounted(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight' || e.key === ' ') {
        nextSlide()
      } else if (e.key === 'ArrowLeft') {
        prevSlide()
      }
    }
    
    window.addEventListener('keydown', handleKeyDown)
    
    onUnmounted(() => {
      window.removeEventListener('keydown', handleKeyDown)
    })
  })
}